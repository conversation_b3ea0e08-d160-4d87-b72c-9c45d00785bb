terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Get current project information
data "google_project" "project" {
  project_id = var.project_id
}

# Service Account for PDF to Text Function
resource "google_service_account" "pdf_function_sa" {
  project      = var.project_id
  account_id   = "sa-tts-${var.environment}-pdf"
  display_name = "TTS PDF Processing Function SA (${var.environment})"
  description  = "Service account for PDF to text processing function in ${var.environment} environment"
}

# Service Account for Chapter TTS Function  
resource "google_service_account" "tts_function_sa" {
  project      = var.project_id
  account_id   = "sa-tts-${var.environment}-tts"
  display_name = "TTS Chapter Processing Function SA (${var.environment})"
  description  = "Service account for chapter TTS processing function in ${var.environment} environment"
}

# Service Account for Admin Publisher
resource "google_service_account" "admin_publisher_sa" {
  project      = var.project_id
  account_id   = "sa-tts-${var.environment}-admin"
  display_name = "TTS Admin Publisher SA (${var.environment})"
  description  = "Service account for admin site to publish messages in ${var.environment} environment"
}

# PDF Function Permissions
# Storage Object Viewer for reading PDFs
resource "google_storage_bucket_iam_member" "pdf_function_storage_viewer" {
  bucket = var.data_bucket_name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.pdf_function_sa.email}"

  # Optional: Restrict to specific prefixes using IAM conditions
  dynamic "condition" {
    for_each = var.enable_iam_conditions ? [1] : []
    content {
      title       = "Restrict to books prefix"
      description = "Allow access only to books/ prefix"
      expression  = "resource.name.startsWith(\"projects/_/buckets/${var.data_bucket_name}/objects/books/\")"
    }
  }
}

# Storage Object Admin for writing extracted text
resource "google_storage_bucket_iam_member" "pdf_function_storage_admin" {
  bucket = var.data_bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.pdf_function_sa.email}"

  dynamic "condition" {
    for_each = var.enable_iam_conditions ? [1] : []
    content {
      title       = "Restrict to text extraction paths"
      description = "Allow admin access only to text-extracted-chapters/ prefix"
      expression  = "resource.name.startsWith(\"projects/_/buckets/${var.data_bucket_name}/objects/books/\")"
    }
  }
}

# Pub/Sub Publisher for chapter messages
resource "google_pubsub_topic_iam_member" "pdf_function_publisher" {
  project = var.project_id
  topic   = var.chapter_topic_name
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# Firestore User for PDF function (local project - legacy)
resource "google_project_iam_member" "pdf_function_firestore" {
  project = var.project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# Cross-project Firestore User for PDF function
resource "google_project_iam_member" "pdf_function_firestore_cross_project" {
  project = var.firestore_project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# Secret Manager Secret Accessor for PDF function
resource "google_secret_manager_secret_iam_member" "pdf_function_secret_accessor" {
  project   = var.project_id
  secret_id = var.gemini_secret_name
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# Secret Manager Secret Accessor for TTS function
resource "google_secret_manager_secret_iam_member" "tts_function_secret_accessor" {
  project   = var.project_id
  secret_id = var.gemini_secret_name
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.tts_function_sa.email}"
}

# Cloud Logging for PDF function
resource "google_project_iam_member" "pdf_function_logs_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# Eventarc receiver for PDF function
resource "google_project_iam_member" "pdf_function_eventarc_receiver" {
  project = var.project_id
  role    = "roles/eventarc.eventReceiver"
  member  = "serviceAccount:${google_service_account.pdf_function_sa.email}"
}

# TTS Function Permissions
# Pub/Sub Subscriber for chapter messages (only if subscription is created by Terraform)
resource "google_pubsub_subscription_iam_member" "tts_function_subscriber" {
  count        = var.create_chapter_subscription ? 1 : 0
  project      = var.project_id
  subscription = "${var.chapter_topic_name}-sub"
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${google_service_account.tts_function_sa.email}"
}

# Storage Object Viewer for reading chapter text
resource "google_storage_bucket_iam_member" "tts_function_storage_viewer" {
  bucket = var.data_bucket_name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.tts_function_sa.email}"

  dynamic "condition" {
    for_each = var.enable_iam_conditions ? [1] : []
    content {
      title       = "Restrict to text files"
      description = "Allow access only to text-extracted-chapters/ prefix"
      expression  = "resource.name.startsWith(\"projects/_/buckets/${var.data_bucket_name}/objects/books/\")"
    }
  }
}

# Storage Object Admin for writing audio files
resource "google_storage_bucket_iam_member" "tts_function_storage_admin" {
  bucket = var.data_bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.tts_function_sa.email}"

  dynamic "condition" {
    for_each = var.enable_iam_conditions ? [1] : []
    content {
      title       = "Restrict to audio paths"
      description = "Allow admin access only to audio/ prefix"
      expression  = "resource.name.startsWith(\"projects/_/buckets/${var.data_bucket_name}/objects/books/\")"
    }
  }
}

# Firestore User for TTS function (local project - legacy)
resource "google_project_iam_member" "tts_function_firestore" {
  project = var.project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.tts_function_sa.email}"
}

# Cross-project Firestore User for TTS function
resource "google_project_iam_member" "tts_function_firestore_cross_project" {
  project = var.firestore_project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.tts_function_sa.email}"
}

# Cloud Logging for TTS function
resource "google_project_iam_member" "tts_function_logs_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.tts_function_sa.email}"
}

# Text-to-Speech API access for TTS function  
# Note: TTS roles will be configured manually via console

# Admin Publisher Permissions
# Pub/Sub Publisher for admin integration
resource "google_pubsub_topic_iam_member" "admin_publisher" {
  project = var.project_id
  topic   = var.chapter_topic_name
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${google_service_account.admin_publisher_sa.email}"
}

# Cloud Logging for admin publisher
resource "google_project_iam_member" "admin_publisher_logs_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.admin_publisher_sa.email}"
}

# Cloud Run Invoker Permissions
# These are needed for Google-managed service accounts to invoke the functions

# Allow Eventarc service account to invoke PDF function (for GCS triggers)
resource "google_cloud_run_service_iam_member" "pdf_function_eventarc_invoker" {
  project  = var.project_id
  location = var.region
  service  = var.pdf_function_name
  role     = "roles/run.invoker"
  member   = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-eventarc.iam.gserviceaccount.com"
}

# Allow Pub/Sub service account to invoke TTS function (for Pub/Sub triggers)
resource "google_cloud_run_service_iam_member" "tts_function_pubsub_invoker" {
  project  = var.project_id
  location = var.region
  service  = var.tts_function_name
  role     = "roles/run.invoker"
  member   = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}

# Additional IAM bindings (custom)
resource "google_project_iam_member" "additional_project_bindings" {
  for_each = var.additional_project_iam_bindings

  project = var.project_id
  role    = each.value.role
  member  = each.value.member

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }
}

# Service Account Keys (optional, for external integrations)
resource "google_service_account_key" "admin_publisher_key" {
  count = var.create_sa_keys ? 1 : 0

  service_account_id = google_service_account.admin_publisher_sa.name
  public_key_type    = "TYPE_X509_PEM_FILE"

  # Rotate keys periodically
  keepers = {
    rotation_time = var.sa_key_rotation_time
  }
}