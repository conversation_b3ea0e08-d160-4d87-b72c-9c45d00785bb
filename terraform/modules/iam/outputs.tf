output "pdf_function_sa_email" {
  description = "Email of the PDF processing function service account"
  value       = google_service_account.pdf_function_sa.email
}

output "pdf_function_sa_id" {
  description = "ID of the PDF processing function service account"
  value       = google_service_account.pdf_function_sa.id
}

output "pdf_function_sa_name" {
  description = "Name of the PDF processing function service account"
  value       = google_service_account.pdf_function_sa.name
}

output "tts_function_sa_email" {
  description = "Email of the TTS function service account"
  value       = google_service_account.tts_function_sa.email
}

output "tts_function_sa_id" {
  description = "ID of the TTS function service account"
  value       = google_service_account.tts_function_sa.id
}

output "tts_function_sa_name" {
  description = "Name of the TTS function service account"
  value       = google_service_account.tts_function_sa.name
}

output "admin_publisher_sa_email" {
  description = "Email of the admin publisher service account"
  value       = google_service_account.admin_publisher_sa.email
}

output "admin_publisher_sa_id" {
  description = "ID of the admin publisher service account"
  value       = google_service_account.admin_publisher_sa.id
}

output "admin_publisher_sa_name" {
  description = "Name of the admin publisher service account"
  value       = google_service_account.admin_publisher_sa.name
}

output "admin_publisher_sa_key" {
  description = "Private key for admin publisher service account (if created)"
  value       = var.create_sa_keys ? google_service_account_key.admin_publisher_key[0].private_key : null
  sensitive   = true
}