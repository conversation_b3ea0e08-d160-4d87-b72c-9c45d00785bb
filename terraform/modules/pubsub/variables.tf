variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "create_chapter_subscription" {
  description = "Whether to create the chapter processing subscription. Defaults to false since Cloud Functions Gen 2 with Pub/Sub triggers automatically create push subscriptions."
  type        = bool
  default     = false
}

# Topic configuration
variable "chapter_topic_name" {
  description = "Name of the main chapter processing topic"
  type        = string
}

variable "dead_letter_topic_name" {
  description = "Name of the dead letter topic"
  type        = string
}

variable "message_retention_duration" {
  description = "Message retention duration for topics"
  type        = string
  default     = "604800s"  # 7 days
}

variable "dead_letter_message_retention_duration" {
  description = "Message retention duration for dead letter topic"
  type        = string
  default     = "2592000s"  # 30 days (longer for debugging)
}

# Subscription configuration
variable "ack_deadline_seconds" {
  description = "Acknowledgment deadline for messages"
  type        = number
  default     = 600  # 10 minutes
  validation {
    condition     = var.ack_deadline_seconds >= 10 && var.ack_deadline_seconds <= 600
    error_message = "ACK deadline must be between 10 and 600 seconds."
  }
}

variable "max_delivery_attempts" {
  description = "Maximum number of delivery attempts before sending to dead letter queue"
  type        = number
  default     = 5
  validation {
    condition     = var.max_delivery_attempts >= 1 && var.max_delivery_attempts <= 100
    error_message = "Max delivery attempts must be between 1 and 100."
  }
}

variable "retain_acked_messages" {
  description = "Whether to retain acknowledged messages"
  type        = bool
  default     = false
}

variable "enable_message_ordering" {
  description = "Whether to enable message ordering"
  type        = bool
  default     = false
}

# Retry policy configuration
variable "retry_minimum_backoff" {
  description = "Minimum backoff duration for retry policy"
  type        = string
  default     = "10s"
}

variable "retry_maximum_backoff" {
  description = "Maximum backoff duration for retry policy"
  type        = string
  default     = "300s"  # 5 minutes
}

# Push configuration (optional)
variable "push_endpoint" {
  description = "Push endpoint URL for push subscriptions"
  type        = string
  default     = null
}

variable "push_oidc_service_account" {
  description = "Service account email for OIDC token authentication"
  type        = string
  default     = null
}

variable "push_oidc_audience" {
  description = "Audience for OIDC token"
  type        = string
  default     = null
}

variable "push_attributes" {
  description = "Attributes to add to push requests"
  type        = map(string)
  default     = {}
}

# Schema configuration (optional)
variable "schema_name" {
  description = "Name of the Pub/Sub schema"
  type        = string
  default     = null
}

variable "schema_type" {
  description = "Type of the schema (AVRO or PROTOCOL_BUFFER)"
  type        = string
  default     = "AVRO"
  validation {
    condition = contains([
      "AVRO",
      "PROTOCOL_BUFFER"
    ], var.schema_type)
    error_message = "Schema type must be either AVRO or PROTOCOL_BUFFER."
  }
}

variable "schema_encoding" {
  description = "Encoding for the schema (JSON or BINARY)"
  type        = string
  default     = "JSON"
  validation {
    condition = contains([
      "JSON",
      "BINARY"
    ], var.schema_encoding)
    error_message = "Schema encoding must be either JSON or BINARY."
  }
}

variable "schema_definition" {
  description = "Schema definition string"
  type        = string
  default     = null
}

# Subscription expiration
variable "subscription_expiration_ttl" {
  description = "TTL for subscription expiration (e.g., '86400s' for 1 day)"
  type        = string
  default     = null
}

# BigQuery configuration (optional)
variable "bigquery_table" {
  description = "BigQuery table for message export (format: project:dataset.table)"
  type        = string
  default     = null
}

variable "bigquery_use_topic_schema" {
  description = "Whether to use the topic schema for BigQuery"
  type        = bool
  default     = false
}

variable "bigquery_write_metadata" {
  description = "Whether to write message metadata to BigQuery"
  type        = bool
  default     = false
}

variable "bigquery_drop_unknown_fields" {
  description = "Whether to drop unknown fields in BigQuery"
  type        = bool
  default     = false
}

# Cloud Storage configuration (optional)
variable "storage_bucket" {
  description = "Cloud Storage bucket for message export"
  type        = string
  default     = null
}

variable "storage_filename_prefix" {
  description = "Filename prefix for Cloud Storage export"
  type        = string
  default     = ""
}

variable "storage_filename_suffix" {
  description = "Filename suffix for Cloud Storage export"
  type        = string
  default     = ""
}

variable "storage_max_duration" {
  description = "Maximum duration before creating a new file"
  type        = string
  default     = "300s"  # 5 minutes
}

variable "storage_max_bytes" {
  description = "Maximum bytes before creating a new file"
  type        = number
  default     = 1000000  # 1MB
}

# IAM configuration
variable "topic_iam_bindings" {
  description = "IAM bindings for the topic"
  type = map(object({
    role    = string
    members = list(string)
    condition = optional(object({
      title       = string
      description = string
      expression  = string
    }))
  }))
  default = {}
}

variable "subscription_iam_bindings" {
  description = "IAM bindings for the subscription"
  type = map(object({
    role    = string
    members = list(string)
    condition = optional(object({
      title       = string
      description = string
      expression  = string
    }))
  }))
  default = {}
}

# Labels
variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}