output "chapter_topic_name" {
  description = "Name of the chapter processing topic"
  value       = google_pubsub_topic.chapter_topic.name
}

output "chapter_topic_id" {
  description = "ID of the chapter processing topic"
  value       = google_pubsub_topic.chapter_topic.id
}

output "chapter_subscription_name" {
  description = "Name of the chapter processing subscription"
  value       = var.create_chapter_subscription ? google_pubsub_subscription.chapter_subscription[0].name : null
}

output "chapter_subscription_id" {
  description = "ID of the chapter processing subscription"
  value       = var.create_chapter_subscription ? google_pubsub_subscription.chapter_subscription[0].id : null
}

output "dead_letter_topic_name" {
  description = "Name of the dead letter topic"
  value       = google_pubsub_topic.dead_letter_topic.name
}

output "dead_letter_topic_id" {
  description = "ID of the dead letter topic"
  value       = google_pubsub_topic.dead_letter_topic.id
}

output "dead_letter_subscription_name" {
  description = "Name of the dead letter subscription"
  value       = google_pubsub_subscription.dead_letter_subscription.name
}

output "dead_letter_subscription_id" {
  description = "ID of the dead letter subscription"
  value       = google_pubsub_subscription.dead_letter_subscription.id
}

output "schema_name" {
  description = "Name of the schema (if created)"
  value       = var.schema_definition != null ? google_pubsub_schema.message_schema[0].name : null
}

output "schema_id" {
  description = "ID of the schema (if created)"
  value       = var.schema_definition != null ? google_pubsub_schema.message_schema[0].id : null
}