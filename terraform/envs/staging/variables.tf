variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for resources"
  type        = string
  default     = "asia-southeast1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Storage configuration
variable "data_bucket_name" {
  description = "Name of the GCS bucket for data storage"
  type        = string
}

# Pub/Sub configuration
variable "chapter_topic_name" {
  description = "Name of the Pub/Sub topic for chapter processing"
  type        = string
  default     = "tts-dev-chapter-process"
}

variable "dead_letter_topic_name" {
  description = "Name of the Pub/Sub dead letter topic"
  type        = string
  default     = "tts-dev-dlq"
}

variable "max_delivery_attempts" {
  description = "Maximum delivery attempts for Pub/Sub messages"
  type        = number
  default     = 5
}

# Firestore configuration
variable "firestore_location" {
  description = "Location for Firestore database"
  type        = string
  default     = "asia-southeast1"
}

# PDF to Text Function configuration
variable "pdf_function_memory" {
  description = "Memory allocation for PDF processing function"
  type        = string
  default     = "4096Mi"
}

variable "pdf_function_timeout" {
  description = "Timeout for PDF processing function"
  type        = string
  default     = "1800s"  # 30 minutes
}

variable "pdf_function_max_instances" {
  description = "Maximum instances for PDF processing function"
  type        = number
  default     = 10
}

variable "pdf_function_min_instances" {
  description = "Minimum instances for PDF processing function"
  type        = number
  default     = 0
}

variable "pdf_function_concurrency" {
  description = "Concurrency per instance for PDF processing function"
  type        = number
  default     = 1
}

# Chapter TTS Function configuration
variable "tts_function_memory" {
  description = "Memory allocation for TTS function"
  type        = string
  default     = "2048Mi"
}

variable "tts_function_timeout" {
  description = "Timeout for TTS function"
  type        = string
  default     = "540s"  # 9 minutes
}

variable "tts_function_max_instances" {
  description = "Maximum instances for TTS function"
  type        = number
  default     = 50
}

variable "tts_function_min_instances" {
  description = "Minimum instances for TTS function"
  type        = number
  default     = 0
}

variable "tts_function_concurrency" {
  description = "Concurrency per instance for TTS function"
  type        = number
  default     = 5
}

# Monitoring configuration
variable "notification_channels" {
  description = "List of notification channel IDs for alerting"
  type        = list(string)
  default     = []
}

# Labels
variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default = {
    environment = "dev"
    project     = "book-tts"
    managed_by  = "terraform"
  }
}