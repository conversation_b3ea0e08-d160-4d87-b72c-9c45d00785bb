# GCP Project Configuration
project_id = "fonos-audio"
region     = "asia-southeast1"

# Environment
environment = "staging"

# Storage Configuration
data_bucket_name = "fonos-staging"  # Using existing bucket

# Pub/Sub Configuration
chapter_topic_name       = "tts-staging-chapter-process"
dead_letter_topic_name   = "tts-staging-dlq"
max_delivery_attempts    = 5

# Firestore Configuration
firestore_location = "asia-southeast1"

# PDF to Text Function Configuration
pdf_function_memory        = "4096Mi"
pdf_function_timeout       = "1800s"  # 30 minutes for large PDFs
pdf_function_max_instances = 10
pdf_function_min_instances = 0
pdf_function_concurrency   = 1

# Chapter TTS Function Configuration
tts_function_memory        = "2048Mi"
tts_function_timeout       = "540s"  # 9 minutes
tts_function_max_instances = 50
tts_function_min_instances = 0
tts_function_concurrency   = 5

# Monitoring Configuration
notification_channels = []  # TODO: Add notification channel IDs if needed

# Labels
labels = {
  environment = "staging"
  project     = "book-tts"
  managed_by  = "terraform"
}