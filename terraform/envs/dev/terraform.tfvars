# GCP Project Configuration
project_id = "fonos-audio"
region     = "asia-southeast1"

# Environment
environment = "dev"

# Storage Configuration
data_bucket_name = "fonos-dev"  # Dev environment bucket

# Pub/Sub Configuration
chapter_topic_name       = "tts-dev-chapter-process"
dead_letter_topic_name   = "tts-dev-dlq"
max_delivery_attempts    = 5

# Firestore Configuration
firestore_location = "asia-southeast1"

# PDF to Text Function Configuration
pdf_function_memory        = "4096Mi"
pdf_function_timeout       = "540s"   # 9 minutes (max for event-triggered functions)
pdf_function_max_instances = 10
pdf_function_min_instances = 0  # Cost optimization for dev
pdf_function_concurrency   = 1

# Chapter TTS Function Configuration
tts_function_memory        = "2048Mi"
tts_function_timeout       = "900s"  # 15 minutes - increased to handle Gemini API timeouts
tts_function_max_instances = 50
tts_function_min_instances = 0  # Cost optimization for dev
tts_function_concurrency   = 5

# Monitoring Configuration
notification_channels = []  # TODO: Add notification channel IDs if needed

# Labels
labels = {
  environment = "dev"
  project     = "book-tts"
  managed_by  = "terraform"
}