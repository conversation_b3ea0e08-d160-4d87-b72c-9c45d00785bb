# Book Text-to-Speech System

Terraform-managed Cloud Functions project that automatically processes PDF books into individual chapter JSON files and generates audio files via text-to-speech processing. The system uses event-driven architecture with two specialized functions connected via Pub/Sub messaging.

## Quick Start

1. **Prerequisites**
   ```bash
   # Install required tools
   gcloud auth login
   export TF_VAR_project_id=fonos-audio
   ```

2. **Deploy Infrastructure**
   ```bash
   cd terraform
   ./deploy-dev.sh                      # Deploys complete system
   ```

3. **Test Processing**
   ```bash
   # Upload PDF to trigger processing
   gsutil cp book.pdf gs://fonos-dev/books/test-book-001/book.pdf
   
   # Monitor logs
   gcloud functions logs read --region=asia-southeast1
   ```

## Architecture

### Two-Function Design

**📄 PDF Processing Function** (`functions/pdf-to-text/`)
- **Trigger**: GCS upload to `books/[id]/book.pdf`
- **Purpose**: Extract chapters using Google Generative AI
- **Output**: Chapter JSON files + Pub/Sub messages
- **Technology**: Standard Python deployment

**🔊 TTS Processing Function** (`functions/chapter-tts/`)  
- **Trigger**: Pub/Sub messages from PDF function
- **Purpose**: Convert chapter text to audio files
- **Output**: MP3 audio files 
- **Technology**: Docker container with ffmpeg + TTS processor

### Processing Flow

```mermaid
graph TD
    A[PDF Upload] --> B[PDF Function]
    B --> C[Chapter Extraction]
    C --> D[Pub/Sub Messages] 
    D --> E[TTS Function]
    E --> F[Audio Generation]
    F --> G[MP3 Files]
    
    B --> H[Firestore Status]
    E --> H
```

1. **PDF Upload** → `gs://bucket/books/[book_id]/book.pdf`
2. **PDF Function** → AI extracts chapters, publishes to Pub/Sub
3. **TTS Function** → Processes each chapter message, generates audio
4. **Progress Tracking** → Firestore documents track processing status

### Output Structure
```
books/[book_id]/
├── book.pdf                     # Original PDF
├── book_manifest.json           # Processing status & outline  
├── text-extracted-chapters/     # Chapter JSON files
│   ├── chapter_01.json
│   ├── chapter_02.json
│   └── ...
└── audio/                       # Generated audio files
    ├── chapter_01.mp3
    ├── chapter_02.mp3
    └── ...
```

## Function Structure

### Organized Architecture
```
functions/
├── pdf-to-text/                 # Function 1: PDF Processing
│   ├── main.py                  # Entry point (process_gcs_file)
│   ├── pdf_to_chapter.py        # PDF processing logic
│   ├── requirements.txt         # Python dependencies
│   └── Dockerfile               # Container configuration
├── chapter-tts/                 # Function 2: TTS Processing
│   ├── main.py                  # Entry point (process_chapter_tts)
│   ├── requirements.txt         # Dependencies
│   ├── Dockerfile               # Container with ffmpeg
│   ├── build.sh                 # Syncs git submodule
│   └── google-voice/            # TTS processor (from git submodule)
└── shared/                      # Common utilities
    ├── firestore_schema.py      # Data models
    └── __init__.py
```

## Data Schemas

### Chapter JSON Schema
Each chapter is saved as a JSON file:

```json
{
  "book_id": "book_001",
  "chapter_id": "chapter_01",
  "chapter_number": 1,
  "chapter_title": "Introduction",
  "content": "extracted chapter text...",
  "word_count": 1250,
  "status": "completed",
  "created_at": "2024-01-15T10:30:00Z",
  "gcs_path": "books/book_001/text-extracted-chapters/chapter_01.json"
}
```

### Book Manifest Schema
Processing progress tracking:

```json
{
  "book_id": "book_001", 
  "total_chapters": 15,
  "pdf_processing_status": "completed",
  "tts_processing_status": "processing",
  "chapters_completed": 12,
  "chapters_failed": [3, 7],
  "created_at": "2024-01-15T10:30:00Z",
  "outline": [...]
}
```

## Deployment

### Infrastructure as Code
All infrastructure managed via Terraform:

```bash
cd terraform

# Deploy complete system (first time)
./deploy-dev.sh

# Update functions only  
./update-functions.sh dev

# Deploy to other environments
./deploy-dev.sh      # Development
# (staging/prod configs available)
```

### Manual Function Updates
```bash  
# Sync google-voice processor submodule
cd functions/chapter-tts
./build.sh

# Deploy via gcloud (alternative to Terraform)
gcloud functions deploy pdf-to-text --source=functions/pdf-to-text
gcloud functions deploy chapter-tts --source=functions/chapter-tts
```

## Development

### PDF Processing Function
```bash
cd functions/pdf-to-text

# Install dependencies
pip install -r requirements.txt

# Test locally (requires GEMINI_API_KEY)
python main.py
```

### TTS Processing Function
```bash  
cd functions/chapter-tts

# Sync google-voice processor from git submodule
./build.sh

# Install dependencies  
pip install -r requirements.txt

# Test Docker build
docker build -t chapter-tts .
```

### Code Quality
```bash
# Format code (in any function directory)
black .

# Type checking
mypy main.py

# Run tests
pytest
```

## Key Features

### ✅ **Event-Driven Processing**
- GCS triggers for automatic PDF processing
- Pub/Sub messaging for scalable chapter processing  
- Firestore state management and progress tracking

### ✅ **Organized Function Structure**
- Clean separation between PDF and TTS processing
- Docker containers for complex dependencies (ffmpeg)
- Git submodule integration for external components

### ✅ **Terraform Infrastructure**  
- Infrastructure as Code approach
- Multi-environment support (dev/staging/prod)
- Automatic API enablement and IAM configuration

### ✅ **Scalable Architecture**
- Parallel chapter processing  
- Dead letter queues for error handling
- Configurable concurrency and resource limits

### ✅ **Audio Processing**
- System-level ffmpeg via Docker containers
- google-voice TTS processor from git submodule
- Multiple audio format support

## Configuration

### Environment Variables (Managed by Terraform)
- `GEMINI_API_KEY` - Google Generative AI API key (Secret Manager)
- `ENVIRONMENT` - Environment name (dev/staging/prod)
- `DATA_BUCKET_NAME` - GCS bucket for file storage
- `CHAPTER_TOPIC_NAME` - Pub/Sub topic for chapter messages

### Project Setup
1. **Authentication**: `gcloud auth login`
2. **Set Project**: `export TF_VAR_project_id=fonos-audio`
3. **Deploy**: `cd terraform && ./deploy-dev.sh`

## Monitoring & Troubleshooting

### Check Processing Status
```bash
# Function logs
gcloud functions logs read --region=asia-southeast1

# GCS files
gsutil ls gs://fonos-dev/books/your-book-id/

# Pub/Sub metrics  
gcloud pubsub topics list
gcloud pubsub subscriptions list
```

### Common Issues
- **Missing API Key**: Verify Secret Manager has `gemini-api-key`
- **Docker Build**: Ensure `build.sh` synced google-voice processor
- **Memory Limits**: Large PDFs may need increased memory allocation
- **Timeout**: Very long books may exceed function timeout

## Technology Stack

- **☁️ Google Cloud Functions Gen 2** - Serverless function execution
- **🏗️ Terraform** - Infrastructure as Code management  
- **🤖 Google Generative AI (Gemini)** - PDF chapter extraction
- **🔊 Google Voice TTS Processor** - Audio synthesis via git submodule
- **📦 Docker** - Containerization for ffmpeg dependencies
- **📨 Pub/Sub** - Event-driven messaging between functions
- **🔥 Firestore** - Document database for state management  
- **☁️ Cloud Storage** - File storage for PDFs, JSONs, and audio

## Next Steps

The system is production-ready for:
- ✅ Automatic PDF processing  
- ✅ Parallel chapter extraction
- ✅ Audio file generation
- ✅ Progress tracking
- ✅ Multi-environment deployment

Consider adding:
- 📊 Monitoring dashboards  
- 🔔 Processing notifications
- 📱 Web interface for uploads
- 🎛️ Audio quality controls