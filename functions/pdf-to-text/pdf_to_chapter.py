

import json
import base64
import asyncio
import time
import random
import os
from typing import List, Dict, Any, Optional, Callable, TypedDict
from dataclasses import dataclass
import concurrent.futures
from PyPDF2 import PdfReader, PdfWriter
import io
import google.generativeai as genai
from dotenv import load_dotenv
import fitz

# Load environment first
load_dotenv()

# Configure gRPC settings for generative AI to prevent resource exhaustion
os.environ.setdefault('GRPC_VERBOSITY', 'ERROR')
os.environ.setdefault('GRPC_TRACE', '')
os.environ.setdefault('GRPC_POLL_STRATEGY', 'poll')
os.environ.setdefault('GRPC_MAX_CONCURRENT_STREAMS', '5')
os.environ.setdefault('GRPC_ENABLE_FORK_SUPPORT', '1')
# Type definitions
class ChapterOutline(TypedDict):
    chapter_number: int
    chapter_title: str
    start_page: int
    end_page: int

@dataclass
class Chapter:
    chapter_number: int
    chapter_title: str
    start_page: int
    end_page: int
    status: str = 'pending'  # 'pending', 'processing', 'completed', 'error'
    content: Optional[str] = None
    errorMessage: Optional[str] = None

class BookProcessor:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('API_KEY')
        if not self.api_key:
            raise ValueError("The API_KEY environment variable is not set. Please set it in the application's environment settings.")
        
        # Configure generative AI with retry logic for gRPC issues
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
        except Exception as genai_error:
            # Retry after a short delay for gRPC initialization issues
            print(f"Generative AI initialization failed, retrying: {genai_error}")
            time.sleep(1)
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                print("Generative AI initialization retry successful")
            except Exception as retry_error:
                print(f"Generative AI initialization retry also failed: {retry_error}")
                raise genai_error
    
    async def _retry_with_exponential_backoff(self, func, *args, **kwargs):
        """Retry function with exponential backoff for 429 errors"""
        max_retries = 5
        base_delay = 15.0  # Start with 15 seconds
        max_delay = 60.0  # Maximum delay of 60 seconds
        
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_str = str(e)
                
                # Check for 429 rate limit errors
                if "429" in error_str or "Resource has been exhausted" in error_str or "quota" in error_str.lower():
                    if attempt == max_retries - 1:
                        print(f"Final retry attempt failed for 429 error after {max_retries} attempts: {e}")
                        print("Throwing error back to trigger function-level retry with Cloud Functions exponential backoff")
                        raise  # Let Cloud Functions handle retry at function level
                    
                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.1, 0.5) * delay
                    total_delay = delay + jitter
                    
                    print(f"429 rate limit hit, retrying in {total_delay:.2f} seconds (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(total_delay)
                    continue
                else:
                    # For non-429 errors, don't retry
                    raise
        
        # This shouldn't be reached, but just in case
        raise Exception(f"Max retries ({max_retries}) exceeded")

    def _pdf_to_base64(self, pdf_bytes: bytes) -> str:
        """Convert PDF bytes to base64 string."""
        return base64.b64encode(pdf_bytes).decode('utf-8')
    def _detect_page_orientation(self, pdf_path: str) -> str:
        """Detect if the PDF pages are primarily landscape or portrait."""
        try:
            doc = fitz.open(pdf_path)
            landscape_count = 0
            portrait_count = 0
            
            # Check first 10 pages to determine orientation
            pages_to_check = min(10, len(doc))
            
            for page_num in range(pages_to_check):
                page = doc[page_num]
                width = page.rect.width
                height = page.rect.height
                
                if width > height:
                    landscape_count += 1
                else:
                    portrait_count += 1
            
            doc.close()
            return "landscape" if landscape_count > portrait_count else "portrait"
        except Exception as e:
            print(f"Error detecting orientation, defaulting to portrait: {e}")
            return "portrait"
    def get_total_pages(self, pdf_path: str) -> int:
        """Get the total number of pages in a PDF."""
        try:
            doc = fitz.open(pdf_path)
            total_pages = doc.page_count
            doc.close()
            return total_pages
        except Exception as e:
            print(f"Error getting total pages: {e}")
            return 0

    async def find_table_of_contents_pages(
        self, 
        pdf_path: str, 
        on_progress: Callable[[str], None]
    ) -> Dict[str, int]:
        """
        Pre-analysis Step: Scan the beginning of the PDF to find the Table of Contents.
        This avoids sending a huge file just to find the outline.
        """
        on_progress('Locating Table of Contents...')
        
        reader = PdfReader(pdf_path)
        total_pages = len(reader.pages)
        
        # Heuristic: Check the first 50 pages for the ToC
        pages_to_scan = min(total_pages, 50)
        
        # Create a new PDF with just the first pages
        writer = PdfWriter()
        for i in range(total_pages):
            writer.add_page(reader.pages[i])
        
        # Convert to bytes
        pdf_buffer = io.BytesIO()
        writer.write(pdf_buffer)
        pdf_bytes = pdf_buffer.getvalue()
        pdf_base64 = self._pdf_to_base64(pdf_bytes)

        toc_location_schema = {
            "type": "object",
            "properties": {
                "table_of_contents": {
                    "type": "object",
                    "description": "The location of the table of contents.",
                    "properties": {
                        "start_page": {"type": "integer", "description": "The page number where the Table of Contents begins."},
                        "end_page": {"type": "integer", "description": "The page number where the Table of Contents ends."}
                    },
                    "required": ["start_page", "end_page"]
                }
            },
            "required": ["table_of_contents"]
        }

        prompt = """
Analyze the provided initial pages of a PDF document to locate the Table of Contents.

## Phase 1: Standard Table of Contents Detection
Your task is to identify the start and end page numbers of the Table of Contents section.
- The Table of Contents might be titled "Contents", "Table of Contents", "Mục lục", or similar.
- The page numbers returned must correspond to the page numbers of the original document.
- If a clear Table of Contents is found, return the result immediately in JSON format.

## Phase 2: Tree of Thoughts Chapter Detection (ONLY if no ToC found)
**Execute ONLY if Phase 1 fails to locate a Table of Contents**

Use Tree of Thoughts approach to systematically find chapters and determine their page ranges:

### Step 1: Chapter Detection Strategy Selection

**Branch A: Textual Pattern Recognition**
- Search for chapter indicators: "Chapter", "Chương", "Part", "Phần", "Section"
- Look for numbering patterns: "Chapter 1", "1.", "I.", "First", "Thứ nhất"
- Identify title formatting: ALL CAPS, Title Case, bold text
- Check positioning: centered, left-aligned, specific indentation

**Branch B: Visual Structure Analysis**
- Detect formatting cues: font size changes, bold text, different fonts
- Identify page breaks: new chapters typically start on fresh pages
- Look for whitespace patterns: extra spacing before/after titles
- Find decorative elements: lines, symbols, ornamental separators

**Branch C: Content Flow Analysis**
- Detect narrative breaks: significant topic shifts or content changes
- Identify writing style changes: different voice or perspective
- Look for logical transitions: conclusions followed by new beginnings
- Find self-references: text mentioning "this chapter" or "next chapter"

### Step 2: Systematic Chapter Location Detection

For each detected chapter, apply multiple identification methods:

**Start Page Detection:**
- Method 1: Extract printed page number where chapter title appears
- Method 2: If no visible page number, scan nearby pages for numbering pattern
- Method 3: Calculate from document structure and known reference points
- Method 4: Use consistent patterns observed from other chapters

**End Page Calculation Tree:**
- **Primary Method**: Find next chapter's start page → end_page = next_start_page - 1
- **Content Analysis**: Scan forward to identify natural chapter conclusion
- **Pattern Recognition**: Use document's consistent chapter length patterns
- **Structure Analysis**: Look for formatting changes indicating chapter boundary

### Step 3: Chapter Validation and Organization

**Validation Checks:**
- Verify start_page < end_page for all chapters
- Ensure no overlapping page ranges
- Confirm logical reading order and sequence
- Validate reasonable chapter lengths and content coverage

**Organization Process:**
- Sort chapters by start_page in ascending order
- Assign sequential chapter_number (1, 2, 3, ...)
- Clean and standardize chapter titles
- Handle special cases (Prologue, Introduction, Epilogue)

### Step 4: Comprehensive Coverage Verification

**Gap Analysis:**
- Identify any gaps between chapter page ranges
- Search gap areas for missed chapters using relaxed criteria
- Ensure complete document coverage with no missing content

**Final Validation:**
- Confirm all narrative content is assigned to chapters
- Verify page numbers correspond to printed document pages
- Ensure logical flow and completeness of chapter sequence

## Output Requirements:

**If Table of Contents is found (Phase 1):**
```json
{
  "table_of_contents": {
    "start_page": [integer],
    "end_page": [integer]
  }
}
```

**If no Table of Contents found (Phase 2 - Chapter Detection):**
```json
{
  "chapters": [
    {
      "chapter_number": 1,
      "chapter_title": "Chapter Title Here",
      "start_page": [integer],
      "end_page": [integer]
    },
    {
      "chapter_number": 2,
      "chapter_title": "Next Chapter Title",
      "start_page": [integer],
      "end_page": [integer]
    }
  ]
}
```

## Execution Instructions:
1. **First**: Attempt standard ToC detection
2. **If ToC found**: Return ToC location JSON immediately
3. **If no ToC**: Execute Tree of Thoughts chapter detection
4. **Return**: Either ToC location OR complete chapters list with accurate page ranges

## CRITICAL REQUIREMENTS:
- Use PDF page numbers (the actual page position in the PDF file), NOT printed page numbers from document text
- For chapters: start_page = PDF page number containing chapter title
- For chapters: end_page = PDF page number before next chapter starts (or total PDF pages for last chapter)
- Ensure complete coverage with no gaps or overlaps
- Return only one JSON format based on what was found (ToC OR chapters)
"""


        try:
            async def _generate_toc_content():
                return await self.model.generate_content_async(
                    [prompt, {"mime_type": "application/pdf", "data": pdf_base64}],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        response_schema=toc_location_schema
                    )
                )
            
            response = await self._retry_with_exponential_backoff(_generate_toc_content)
            
            json_response = json.loads(response.text)
            if "table_of_contents" in json_response:
                return json_response["table_of_contents"]
            return {"start_page": 0, "end_page": 0}
            
        except Exception as e:
            print(f"Failed to parse ToC location JSON: {e}")
            return {"start_page": 0, "end_page": 0}

    async def get_book_outline(
        self,
        pdf_path: str,
        on_progress: Callable[[str], None],
        toc_pages: Optional[Dict[str, int]] = None
    ) -> List[ChapterOutline]:
        """
        Step 1: Analyze a portion of the PDF (ideally the ToC) to get a structured outline of chapters.
        """
        on_progress('Analyzing book structure to identify chapters...')

        reader = PdfReader(pdf_path)
        total_book_pages = len(reader.pages)
        
        use_toc_flow = False
        
        # If we have ToC pages, try to extract them
        if (toc_pages and toc_pages["start_page"] > 0 and toc_pages["end_page"] > 0 
            and toc_pages["start_page"] <= toc_pages["end_page"]):
            
            on_progress('Extracting Table of Contents for analysis...')
            
            # Page numbers are 1-based, but PyPDF2 uses 0-based indexing
            start_idx = toc_pages["start_page"] - 1
            end_idx = min(toc_pages["end_page"], total_book_pages)
            
            if start_idx < total_book_pages and start_idx >= 0:
                writer = PdfWriter()
                for i in range(start_idx, end_idx):
                    writer.add_page(reader.pages[i])
                
                pdf_buffer = io.BytesIO()
                writer.write(pdf_buffer)
                pdf_bytes = pdf_buffer.getvalue()
                
                analysis_context = f"You are analyzing a snippet containing the Table of Contents of a book. This snippet starts at page {toc_pages['start_page']} of the original document."
                use_toc_flow = True
            else:
                print("Table of Contents page range was invalid. Falling back to full document analysis.")
        
        # Fallback for books without a ToC or if ToC detection fails
        if not use_toc_flow:
            on_progress('Table of Contents not found or invalid. Analyzing full document...')
            analysis_context = f"You are analyzing a full PDF document of {total_book_pages} pages."
            
            if total_book_pages > 1000:
                raise ValueError(f"The document has {total_book_pages} pages and a Table of Contents could not be automatically identified. This exceeds the 1000-page limit for direct analysis.")
            
            # Read entire PDF
            with open(pdf_path, 'rb') as file:
                pdf_bytes = file.read()
        
        pdf_base64 = self._pdf_to_base64(pdf_bytes)

        outline_schema = {
            "type": "object",
            "properties": {
                "audiobook_outline": {
                    "type": "array",
                    "description": "A list of all chapters in the book.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "chapter_number": {"type": "integer", "description": "The sequential number of the chapter."},
                            "chapter_title": {"type": "string", "description": "The full title of the chapter."},
                            "start_page": {"type": "integer", "description": "The page number where the chapter begins."},
                            "end_page": {"type": "integer", "description": "The page number where the chapter ends."}
                        },
                        "required": ["chapter_number", "chapter_title", "start_page", "end_page"]
                    }
                }
            },
            "required": ["audiobook_outline"]
        }

        prompt = f"""{analysis_context}
Your task is to analyze the provided document section and extract a structured list of all chapters for an audiobook.
CRITICAL INSTRUCTIONS:
1. **Follow Table of Contents Order:** Extract chapters in the exact order they appear in the Table of Contents, regardless of their position in the book.
2. **Extract All Listed Sections:** Include all narrative sections listed in the Table of Contents (e.g., Introduction, Preface, Foreword, Acknowledgments, Chapter 1, Chapter 2, etc.) in their listed order.
3. **Include Acknowledgments:** If acknowledgments/thanks sections are listed in the Table of Contents, include them at whatever position they appear (beginning, middle, or end).
4. **Parts vs Chapters:** Parts I, II, III,... are not considered chapters, but extract the actual chapters within those parts.
5. **ACCURATE PAGE NUMBERS:** This is the most important instruction. The 'start_page' you extract MUST be the page number *printed in the text* (e.g., in the Table of Contents). DO NOT use the page numbers of the PDF file you are analyzing.
- **Roman Numeral Conversion:** If page numbers are shown in Roman numerals (i, ii, iii, iv, v, vi, vii, viii, ix, x, xi, xii, xiii, xiv, xv, xvi, xvii, xviii, xix, xx, etc.), convert them to negative decimal numbers (-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, etc.) before using them.
- For example, if the text says "Introduction ..... xi", convert xi to -11 and return a start_page of -11.
6. **Infer End Pages:** A chapter's 'end_page' is calculated as follows:
- For chapters with Roman numeral pages: The end_page is one less than the next chapter's start_page. 
- For the last chapter in the Roman numeral section (the one with the highest Roman numeral): Its end_page should be calculated based on the highest Roman numeral found in the Table of Contents. For example, if the highest Roman numeral is xviii (18), then convert it to -18, and the last Roman chapter's end_page would be -18.
- For regular number chapters: The end_page is one less than the next chapter's start_page.
- For the very last chapter of the entire book: Its 'end_page' MUST be the total page count of the book.
7. **Exclusions:** IGNORE entries for the Table of Contents itself, Appendices, Indices, or similar reference sections (but INCLUDE all narrative content including acknowledgments).
8. **Spelling Correction**: If any chapter titles contain spelling errors, correct them in the output while maintaining the original meaning and context of the title.
For each identified chapter, you must provide:
- chapter_number: The sequential number of the chapter (e.g., 1, 2, 3).
- chapter_title: The full title of the section/chapter as it appears in the Table of Contents (including acknowledgments, introductions, etc. if listed).
- start_page: The page number where the chapter begins (converted to negative decimal if originally in Roman numerals).
- end_page: The page number where the chapter ends (calculated as per instruction #6).
The final output must be a single, clean JSON object matching the provided schema."""

        try:
            async def _generate_outline_content():
                return await self.model.generate_content_async(
                    [prompt, {"mime_type": "application/pdf", "data": pdf_base64}],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        response_schema=outline_schema,
                        max_output_tokens=65536
                    )
                )
            
            response = await self._retry_with_exponential_backoff(_generate_outline_content)

            print(f"Full AI response from get_book_outline: {response}")

            response_text = response.text
            
            if not response_text or response_text.strip() == '':
                finish_reason = getattr(response.candidates[0], 'finish_reason', 'Unknown') if response.candidates else 'Unknown'
                error_detail = f"AI returned an empty response. Finish Reason: {finish_reason}."
                raise ValueError(error_detail)

            # Clean the response text
            raw_text = response_text.strip()
            if raw_text.startswith('```json'):
                raw_text = raw_text.replace('```json', '', 1).replace('```', '', 1).strip()

            json_response = json.loads(raw_text)
            
            if "audiobook_outline" not in json_response or not isinstance(json_response["audiobook_outline"], list):
                raise ValueError("Invalid outline structure received from AI: 'audiobook_outline' array is missing.")

            # Post-process to fix any AI-inferred end-page issues
            outline = sorted(json_response["audiobook_outline"], key=lambda x: x["start_page"])
            print(f"Detected outline: {outline}")
            if not outline:
                return []

            # Fix overlapping end pages
            for i in range(len(outline) - 1):
                if outline[i]["end_page"] >= outline[i + 1]["start_page"]:
                    outline[i]["end_page"] = outline[i + 1]["start_page"] - 1

            

            return outline

        except Exception as e:
            print(f"Error during outline processing: {e}")
            if "AI returned an empty response" in str(e):
                raise e
            
            finish_reason = getattr(response.candidates[0], 'finish_reason', 'N/A') if 'response' in locals() and response.candidates else 'N/A'
            raise ValueError(f"Failed to parse outline. Parser error: {str(e)}. (AI Finish Reason: {finish_reason})")

    async def _extract_text_for_chapter(self, pdf_path: str, chapter: ChapterOutline, orientation: str) -> str:
        """For a given chapter, extract the clean body text from its page range."""
        reader = PdfReader(pdf_path)
        total_pages = len(reader.pages)
        
        # Create page indices (1-based to 0-based conversion)
        page_indices = []
        if orientation == "landscape":
            for i in range(chapter["start_page"]//2 - 3, min(chapter["end_page"]//2 + 3, total_pages)):
                if i >= 0:
                    page_indices.append(i)
            if not page_indices:
                raise ValueError(f"Invalid page range [{chapter['start_page']}-{chapter['end_page']}] for chapter. No pages to process.")
        else:
            for i in range(chapter["start_page"] - 3, min(chapter["end_page"] + 3, total_pages)):
                if i >= 0:
                    page_indices.append(i)

            if not page_indices:
                raise ValueError(f"Invalid page range [{chapter['start_page']}-{chapter['end_page']}] for chapter. No pages to process.")

        # Create new PDF with chapter pages
        writer = PdfWriter()
        for idx in page_indices:
            writer.add_page(reader.pages[idx])

        pdf_buffer = io.BytesIO()
        writer.write(pdf_buffer)
        chapter_pdf_bytes = pdf_buffer.getvalue()
        chapter_pdf_base64 = self._pdf_to_base64(chapter_pdf_bytes)

        prompt = f"""
You need to extract the complete main body text from the provided PDF pages for the chapter titled "{chapter['chapter_title']}".
Use Tree of Thoughts approach to ensure accurate extraction:

## Step 1: Initial Analysis
First, analyze the PDF pages and identify:
- **Page layout structure (single column, two-column, or multi-column)**
- **Column boundaries and reading order (left-to-right for multi-column layouts)**
- What constitutes main body text vs. auxiliary elements
- Where headers/footers appear on each page
- Location of images, diagrams, and captions
- Structure of headings and sections
- **Chapter titles and redundant chapter headers to exclude**
- **DROP CAPS and decorative initial letters that belong to paragraph text**
- **Separated formatting elements that should be part of continuous text**
- **Text flow patterns across columns and pages**

## Step 2: Layout-Specific Reading Order Detection
### For Multi-Column Pages:
- **Identify column structure (typically 2 columns)**
- **CRITICAL: A single page may contain content from multiple chapters mixed across columns**
- **ASSUME target chapter content may exist in BOTH columns until proven otherwise**
- **Identify ALL content belonging to target chapter regardless of column position**
- **Target chapter content pattern may be:**
  - **Left column: [Target chapter content] + [References/Bibliography] + [Chapter transition]**
  - **Right column: [New chapter title] + [New chapter content]**
- **Extract ALL target chapter content from wherever it appears on the page**

### Chapter Content Detection Strategy:
- **Step 1: Identify all chapter titles/transitions on the page**
- **Step 2: Map content between these transitions to determine ownership**
- **Step 3: Extract ALL content belonging to target chapter from both columns**
- **Do NOT assume entire columns belong to one chapter - content ownership is granular**

### Content Ownership Mapping:
- **Main chapter content = extract**
- **References/Bibliography sections ("TÌM HIỂU THÊM", "Tài liệu tham khảo", etc.) = exclude**  
- **Footnotes and citations = exclude**
- **Before new chapter title = may belong to current chapter (if main content)**
- **After new chapter title = belongs to new chapter**
- **Between chapter sections = check if main content or auxiliary material**

### For Single Column Pages:
- Follow standard top-to-bottom reading order
- Identify chapter start/end boundaries within the column

## Step 3: Generate Multiple Extraction Approaches
### Approach A: Comprehensive Content Mapping with Main Content Focus
- **FIRST: Assume target chapter has MAIN CONTENT in BOTH columns until proven otherwise**
- **Identify and EXCLUDE auxiliary sections like references, bibliography, footnotes**
- **Look for section markers: "TÌM HIỂU THÊM", "Tài liệu tham khảo", "References", etc.**
- **Extract pattern: Target chapter main content from left + Target chapter main content from right**
- **EXCLUDE references/bibliography even if they belong to target chapter**
- **Include only narrative, explanatory, and primary instructional content**
- Skip chapter titles, chapter numbers, and redundant headers
- Identify and skip header/footer regions in each column  
- **Detect and merge drop caps with their corresponding paragraph text**
- Extract all target chapter main content following proper reading order
- **Focus on primary content, exclude supplementary materials**

### Approach B: Main Content Classification with Reference Exclusion
- **FIRST: Classify ALL text elements by content type and chapter ownership**
- **Default assumption: main content belongs to target chapter unless clearly marked otherwise**
- **Identify and exclude auxiliary sections:**
  - **References/Bibliography ("TÌM HIỂU THÊM", "References", "Further Reading")**
  - **Footnotes and citations**
  - **Appendices and supplementary materials**
- **Look for explicit chapter transition markers (titles, numbers) to exclude content**
- **A single page may have: [Target chapter main content] + [References to exclude] + [New chapter start]**
- **Extract only main content portions from both columns**
- Classify text elements by type (body, header, footer, caption, drop cap, chapter title, etc.)
- **Exclude chapter titles/numbers AND reference sections**
- **Identify drop caps and merge them with following paragraph text**
- Extract only primary narrative/instructional content belonging to target chapter
- Reassemble in logical reading order for complete main chapter content

### Approach C: Layout-Based Detection with Chapter-Column Boundaries
- **Analyze column structure and identify chapter ownership of each column**
- **If target chapter starts in right column, left column belongs to different chapter**
- **Map reading flow only within columns belonging to target chapter**
- Identify and exclude chapter titles based on formatting and position
- **Detect large decorative letters at paragraph beginnings in target chapter columns only**
- Use layout patterns to distinguish main content from auxiliary text
- **Merge separated initial letters with their paragraph content**
- Extract based on position and formatting consistency while respecting chapter-column boundaries

## Step 4: Evaluate Each Approach
For each approach above, consider:
- **Accuracy in following correct column reading order (left-to-right)**
- **Ability to maintain text coherence across column breaks**
- Accuracy in identifying main body text
- **Ability to correctly handle drop caps and decorative initials in multi-column layout**
- Risk of including unwanted elements (headers/footers/captions)
- Risk of missing important content or disrupting text flow
- Ability to maintain text flow and paragraph structure across columns

## Step 5: Select and Execute Best Approach
Choose the most reliable approach based on the specific layout of these PDF pages, then execute the extraction.

**SPECIAL HANDLING FOR MULTI-COLUMN LAYOUT:**
- **CRITICAL: DEFAULT to including content from BOTH columns for target chapter**
- **Only exclude content when there's clear evidence it belongs to different chapter**
- **Target chapter may have: main content + references + notes + conclusions across both columns**
- **Common pattern: Left column has main chapter content + references, Right column has new chapter**
- **Extract ALL target chapter content regardless of where it appears**
- **Include content that appears BEFORE new chapter titles/markers**
- **Examples:**
  - **Left column: "[TARGET CHAPTER CONTENT]... TÌM HIỂU THÊM [references]"**
  - **Right column: "CHƯƠNG [X] [NEW CHAPTER TITLE] [new chapter content]"**
  - **Extract: ALL target content from left + NONE from right after new chapter marker**

**CONTENT INCLUSION STRATEGY:**
- **Include ALL content before new chapter title appears**
- **Include references, bibliography, notes if they follow target chapter content**  
- **Only exclude content that appears after clear new chapter markers**
- **When in doubt, INCLUDE rather than exclude target chapter content**

**SPECIAL HANDLING FOR CHAPTER CONTENT:**
- **INCLUSION PRINCIPLE: Extract ALL content that belongs to target chapter from entire page**
- **Content belonging to target chapter may include:**
  - **Main narrative text**
  - **References and bibliography sections (TÌM HIỂU THÊM, etc.)**
  - **Chapter conclusions or summaries**
  - **Any content appearing BEFORE new chapter titles**
- **EXCLUDE only:**
  - **Chapter titles and chapter numbers of target chapter (e.g., "CHƯƠNG 3")**
  - **Content appearing AFTER new/different chapter titles**
  - **Headers, footers, page numbers**
- **DEFAULT: If unsure whether content belongs to target chapter, INCLUDE it**
- **CRITICAL: Do not miss any target chapter content due to overly restrictive boundary detection**

**SPECIAL HANDLING FOR DROP CAPS:**
- Look for large single letters or short text fragments at the beginning of paragraphs
- These are often formatted differently (larger font, different positioning)  
- **In multi-column layouts, drop caps may appear at the start of either column**
- Merge them seamlessly with the following text to form complete sentences
- Example: "Ở" + "hầu hết các nước phương Tây..." → "Ở hầu hết các nước phương Tây..."
- **"K" + "hi Steve Jobs quay trở lại..." → "Khi Steve Jobs quay trở lại..."**

## Step 6: Quality Check and Refinement
After extraction, verify:
- **CRITICAL: ALL target chapter content has been captured from both columns**
- **No target chapter content missed due to overly strict boundary detection**
- **Included all relevant sections: main text + references + conclusions + notes**
- **Only excluded content clearly belonging to different chapters (after new chapter titles)**
- **No chapter titles, chapter numbers, or redundant chapter headers of target chapter included**
- No page numbers or repeating headers/footers included  
- No image captions or diagram text included
- Section headings properly included (without formatting marks)
- **All drop caps properly merged with their paragraph text**
- **No orphaned single letters or incomplete sentence beginnings**
- **Text flows logically and completely represents the full target chapter**
- **Content includes complete chapter from start to natural end (before next chapter)**
- **Coherent and complete narrative flow for entire target chapter**

## FINAL OUTPUT REQUIREMENTS:
- Extract ONLY the primary narrative/main body text of the TARGET CHAPTER
- **CRITICAL: Extract only content that belongs to the specified chapter**
- **If chapter starts in right column, ignore left column completely**
- **If chapter ends before page ends, stop extraction at chapter boundary**
- **COMPLETELY EXCLUDE chapter titles, chapter numbers, and any chapter headers**
- INCLUDE section titles/headings as plain text (remove *, #, ** formatting)
- **MERGE all drop caps and decorative initials with their paragraph text**
- IGNORE page headers, footers, page numbers, book titles
- IGNORE image captions and diagram text
- Preserve paragraph breaks and logical text flow within target chapter
- Output as continuous text block with proper paragraph separation
- **Maintain natural reading flow for target chapter content only**

Execute this tree of thoughts process internally, then return ONLY the extracted text with absolutely no additional content.

## CRITICAL OUTPUT REQUIREMENTS:
- NO introductory sentences or explanations
- NO "The following text is..." or similar phrases
- NO metadata, processing notes, or commentary
- NO mention of the extraction process or approach used
- **ENSURE all sentences begin properly with merged drop caps**
- **ENSURE proper reading sequence across columns (left-to-right)**
- START directly with the first complete sentence of the chapter content
- END with the last sentence of the chapter content
- ZERO additional text beyond the pure chapter content
"""

        async def _generate_chapter_content():
            return await self.model.generate_content_async(
                [prompt, {"mime_type": "application/pdf", "data": chapter_pdf_base64}]
            )
        
        response = await self._retry_with_exponential_backoff(_generate_chapter_content)

        response_text = response.text.strip()

        # Check for common AI responses indicating no actual content
        no_content_keywords = ["no text", "no content", "cannot extract", "blank page", "title page", "no main body", "no narrative"]
        lower_response = response_text.lower()

        if len(response_text) < 150 and any(keyword in lower_response for keyword in no_content_keywords):
            raise ValueError(f"AI indicated no valid content was found (e.g., blank pages or title pages). Original response: \"{response_text}\"")

        return response_text

    async def process_chapters_in_parallel(
        self,
        pdf_path: str,
        outline: List[ChapterOutline],
        on_chapter_update: Callable[[Chapter], None],
        concurrency_limit: int = 10
    ) -> None:
        """
        Step 2: Processes all chapters from an outline in parallel, providing real-time
        updates via a callback function.
        """
        semaphore = asyncio.Semaphore(concurrency_limit)
        
        async def process_chapter(chapter_outline: ChapterOutline):
            async with semaphore:
                chapter = Chapter(**chapter_outline)
                
                try:
                    # Notify UI that processing has started
                    chapter.status = 'processing'
                    on_chapter_update(chapter)
                    orientation = self._detect_page_orientation(pdf_path)
                    chapter_text = await self._extract_text_for_chapter(pdf_path, chapter_outline, orientation)

                    # Notify UI of success
                    chapter.status = 'completed'
                    chapter.content = chapter_text
                    on_chapter_update(chapter)

                except Exception as error:
                    error_message = str(error) if error else 'Unknown extraction error'
                    print(f"Error processing chapter \"{chapter.chapter_title}\": {error}")

                    # Notify UI of error
                    chapter.status = 'error'
                    chapter.errorMessage = error_message
                    on_chapter_update(chapter)

        # Process all chapters concurrently
        tasks = [process_chapter(chapter) for chapter in outline]
        await asyncio.gather(*tasks)

    def save_outline_to_json(self, outline: List[ChapterOutline], output_path: str = "book_outline.json"):
        """Save the book outline to a JSON file."""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(outline, f, indent=2, ensure_ascii=False)
            print(f"Book outline saved to: {output_path}")
        except Exception as e:
            print(f"Error saving outline: {e}")

    def save_chapter_text(self, chapter: Chapter, output_dir: str = "chapters"):
        """Save individual chapter text to a file."""
        import os
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        if chapter.content:
            # Clean filename
            safe_title = "".join(c for c in chapter.chapter_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"Chapter_{chapter.chapter_number:02d}_{safe_title}.txt"
            filepath = os.path.join(output_dir, filename)
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"Chapter {chapter.chapter_number}: {chapter.chapter_title}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(chapter.content)
                print(f"Chapter saved to: {filepath}")
            except Exception as e:
                print(f"Error saving chapter {chapter.chapter_number}: {e}")

    def save_all_chapters_combined(self, chapters: List[Chapter], output_path: str = "full_book.txt"):
        """Save all chapters combined into a single text file."""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for chapter in sorted(chapters, key=lambda x: x.chapter_number):
                    if chapter.content and chapter.status == 'completed':
                        f.write(f"Chapter {chapter.chapter_number}: {chapter.chapter_title}\n")
                        f.write("=" * 50 + "\n\n")
                        f.write(chapter.content)
                        f.write("\n\n" + "="*80 + "\n\n")
            print(f"Combined book saved to: {output_path}")
        except Exception as e:
            print(f"Error saving combined book: {e}")

    async def process_and_save_book(
        self,
        pdf_path: str,
        output_dir: str = "output",
        save_individual_chapters: bool = True,
        save_combined_book: bool = True
    ):
        """Complete workflow: process book and save all outputs."""
        import os
        
        # Create main output directory
        os.makedirs(output_dir, exist_ok=True)

         # Detect and log orientation
        orientation = self._detect_page_orientation(pdf_path)
        print(f"Detected book orientation: {orientation}")
        def progress_callback(message: str):
            print(f"Progress: {message}")
        
        processed_chapters = []
        
        def chapter_update_callback(chapter: Chapter):
            print(f"Chapter {chapter.chapter_number} ({chapter.chapter_title}): {chapter.status}")
            
            if chapter.status == 'completed':
                processed_chapters.append(chapter)
                
                # Save individual chapter if requested
                if save_individual_chapters:
                    chapter_dir = os.path.join(output_dir, "chapters")
                    self.save_chapter_text(chapter, chapter_dir)
        
        try:
            total_page = self.get_total_pages(pdf_path)
            # Step 1: Find ToC
            toc_pages = await self.find_table_of_contents_pages(pdf_path, progress_callback)
            print(f"ToC found at pages: {toc_pages}")
            
            # Step 2: Get outline
            outline = await self.get_book_outline(pdf_path, progress_callback, toc_pages)
            page_offset = 0
            if outline[0]["start_page"] < 0:
                for chapter in outline:
                    if chapter["start_page"] < 0:
                        page_offset = max(page_offset, -chapter["end_page"])
                for chapter in outline:
                    if chapter["start_page"] < 0:
                        chapter["start_page"] = -chapter["start_page"] +3
                        chapter["end_page"] = -chapter["end_page"] + 3
                    else:
                        chapter["start_page"] += page_offset + 3
                        chapter["end_page"] += page_offset + 3
            if outline[-1]["end_page"] < total_page:
                outline[-1]["end_page"] = total_page
            print(f"Found {len(outline)} chapters")
            
            # Save outline
            outline_path = os.path.join(output_dir, "book_outline.json")
            self.save_outline_to_json(outline, outline_path)
            
            # Step 3: Process chapters
            await self.process_chapters_in_parallel(pdf_path, outline, chapter_update_callback)
            
            # Step 4: Save combined book if requested
            if save_combined_book:
                combined_path = os.path.join(output_dir, "full_book.txt")
                self.save_all_chapters_combined(processed_chapters, combined_path)
            
            print(f"\nProcessing complete! Files saved in: {output_dir}")
            print(f"Total chapters processed: {len(processed_chapters)}")
            
        except Exception as e:
            print(f" Error during processing: {e}")

# Example usage
async def example_usage():
    processor = BookProcessor()
    
    # Process and save everything
    await processor.process_and_save_book(
        pdf_path="book/book.pdf",
        output_dir="my_book_output",
        save_individual_chapters=True,
        save_combined_book=True
    )

# Uncomment to run example
if __name__ == "__main__":
    asyncio.run(example_usage())
