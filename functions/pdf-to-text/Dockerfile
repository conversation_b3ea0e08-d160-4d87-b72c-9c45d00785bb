# Dockerfile for PDF to Text Function
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy function code
COPY *.py ./

# Set environment variables for Functions Framework
ENV FUNCTION_TARGET=process_gcs_file
ENV FUNCTION_SOURCE=main.py

# Expose port for Cloud Functions
EXPOSE 8080

# Use Functions Framework to run the function
CMD exec functions-framework --target=${FUNCTION_TARGET} --source=${FUNCTION_SOURCE} --port=8080 --host=0.0.0.0