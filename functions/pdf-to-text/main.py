"""
Function 1: PDF to Text Processing with GCS Trigger and Pub/Sub Publishing
Refactored from main.py to separate concerns and align with Terraform migration plan.
"""

import json
import logging
import tempfile
import os
import re
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

import functions_framework
from google.cloud import storage, pubsub_v1, firestore

from pdf_to_chapter import BookProcessor

# Suppress gRPC credential warnings
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="grpc")

# Set explicit authentication environment
os.environ.setdefault('GOOGLE_APPLICATION_CREDENTIALS', '')

# Comprehensive gRPC configuration to prevent resource exhaustion
os.environ.setdefault('GRPC_VERBOSITY', 'ERROR')
os.environ.setdefault('GRPC_TRACE', '')
# Limit concurrent connections and requests
os.environ.setdefault('GRPC_MAX_CONCURRENT_STREAMS', '10')
os.environ.setdefault('GRPC_MAX_CONNECTION_IDLE_MS', '30000')
os.environ.setdefault('GRPC_MAX_CONNECTION_AGE_MS', '300000')
os.environ.setdefault('GRPC_MAX_CONNECTION_AGE_GRACE_MS', '5000')
os.environ.setdefault('GRPC_HTTP2_MAX_PINGS_WITHOUT_DATA', '0')
os.environ.setdefault('GRPC_HTTP2_MIN_TIME_BETWEEN_PINGS_MS', '10000')
# Prevent polling issues in Cloud Functions
os.environ.setdefault('GRPC_POLL_STRATEGY', 'poll')
os.environ.setdefault('GRPC_ENABLE_FORK_SUPPORT', '1')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@functions_framework.cloud_event
def process_gcs_file(cloud_event):
    """
    Cloud Function entry point for GCS object finalized events.
    Processes PDF files uploaded to books/[id]/book.pdf pattern.
    
    Args:
        cloud_event: CloudEvent object containing event data
    """
    try:
        # Extract event data from CloudEvent
        # For GCS events, data contains the GCS event payload
        event_data = cloud_event.data
        
        # Debug logging to understand the event structure
        logger.info(f"CloudEvent type: {getattr(cloud_event, 'type', 'unknown')}")
        logger.info(f"CloudEvent source: {getattr(cloud_event, 'source', 'unknown')}")
        logger.info(f"Event data type: {type(event_data)}")
        logger.info(f"Event data: {event_data}")
        
        # For GCS CloudEvents, bucket and name should be in the data payload
        bucket_name = event_data.get("bucket") if event_data else None
        file_name = event_data.get("name") if event_data else None
        
        if not bucket_name or not file_name:
            logger.warning(f"Missing bucket or file name in event data: bucket={bucket_name}, file={file_name}")
            return {"status": "error", "reason": "Missing bucket or file name in event data"}
        
        logger.info(f"Processing file: {file_name} in bucket: {bucket_name}")
        
        # Only process PDF files in the correct book structure
        if not file_name.endswith('.pdf') or not is_book_pdf(file_name):
            logger.info(f"File {file_name} is not a book PDF, skipping processing")
            return {"status": "skipped", "reason": "Not a book PDF"}
        
        # Process the PDF file
        result = process_pdf_file(bucket_name, file_name)
        logger.info(f"Successfully processed PDF: {file_name}")
        return result
        
    except Exception as e:
        logger.error(f"Critical error in GCS event handler: {str(e)}")
        logger.error(f"CloudEvent data: {getattr(cloud_event, 'data', 'No data available')}")
        
        # Try to extract book_id for error tracking
        book_id = None
        try:
            if hasattr(cloud_event, 'data') and cloud_event.data:
                file_name = cloud_event.data.get("name", "")
                if file_name:
                    book_id = extract_book_id_from_path(file_name)
        except:
            pass
        
        # Update Firestore with error if we have book_id
        if book_id:
            try:
                processor = CloudBookProcessor("", book_id)
                processor.update_book_status("error", error_message=f"Function error: {str(e)}")
            except Exception as db_error:
                logger.error(f"Failed to update error status in Firestore: {db_error}")
        
        # Return error response instead of raising
        return {
            "status": "error", 
            "error": str(e),
            "book_id": book_id,
            "message": "PDF processing failed"
        }


def is_book_pdf(file_name: str) -> bool:
    """Check if the file follows the books/[id]/book.pdf pattern"""
    pattern = r'^books/[^/]+/book\.pdf$'
    return re.match(pattern, file_name) is not None


def extract_book_id_from_path(file_name: str) -> str:
    """Extract book ID from path like books/book_001/book.pdf -> book_001"""
    pattern = r'^books/([^/]+)/book\.pdf$'
    match = re.match(pattern, file_name)
    if match:
        return match.group(1)
    raise ValueError(f"Invalid book path format: {file_name}")


def process_pdf_file(bucket_name: str, file_name: str) -> Dict[str, Any]:
    """Process PDF files and extract chapters"""
    processor = None
    book_id = None
    
    try:
        book_id = extract_book_id_from_path(file_name)
        logger.info(f"Processing PDF for book: {book_id}")
        
        # Initialize processor with environment configuration
        try:
            processor = CloudBookProcessor(bucket_name, book_id)
            logger.info(f"Initialized CloudBookProcessor for book: {book_id}")
        except Exception as init_error:
            logger.error(f"Failed to initialize CloudBookProcessor: {init_error}")
            return {
                "status": "error",
                "error": f"Processor initialization failed: {str(init_error)}",
                "book_id": book_id,
                "message": "Failed to initialize PDF processor"
            }
        
        # Update Firestore with processing status
        try:
            processor.update_book_status("extracting")
            logger.info(f"Updated book status to 'extracting' for book: {book_id}")
        except Exception as status_error:
            logger.warning(f"Failed to update initial status: {status_error}")
            # Continue processing even if status update fails
        
        # Run async processing in the sync context
        try:
            logger.info(f"Starting async processing for book: {book_id}")
            # Always use thread pool approach to avoid gRPC/asyncio conflicts in Cloud Functions
            import concurrent.futures
            import threading
            
            def run_async_in_thread():
                # Create new event loop in thread to isolate from gRPC callbacks
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(process_book_chapters_async(processor, file_name))
                finally:
                    new_loop.close()
                    # Clear the thread-local event loop
                    asyncio.set_event_loop(None)
            
            logger.info("Running async processing in isolated thread")
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_async_in_thread)
                result = future.result(timeout=500)  # 500 second timeout
                
            logger.info(f"Successfully completed async processing for book: {book_id}")
            return result
        except asyncio.TimeoutError:
            error_msg = "PDF processing timed out"
            logger.error(f"{error_msg} for book: {book_id}")
            if processor:
                try:
                    processor.update_book_status("error", error_message=error_msg)
                except:
                    pass
            return {
                "status": "error",
                "error": error_msg,
                "book_id": book_id,
                "message": "PDF processing exceeded time limit"
            }
        except Exception as async_error:
            error_msg = f"Async processing failed: {str(async_error)}"
            logger.error(f"{error_msg} for book: {book_id}")
            if processor:
                try:
                    processor.update_book_status("error", error_message=error_msg)
                except:
                    pass
            return {
                "status": "error",
                "error": error_msg,
                "book_id": book_id,
                "message": "PDF chapter processing failed"
            }
        
    except Exception as e:
        error_msg = f"Critical error processing PDF {file_name}: {str(e)}"
        logger.error(error_msg)
        
        # Update Firestore with error status if possible
        if processor and book_id:
            try:
                processor.update_book_status("error", error_message=str(e))
            except Exception as db_error:
                logger.error(f"Failed to update error status in Firestore: {db_error}")
        
        return {
            "status": "error",
            "error": str(e),
            "book_id": book_id,
            "message": "PDF processing failed with critical error"
        }


async def process_book_chapters_async(processor: 'CloudBookProcessor', file_name: str) -> Dict[str, Any]:
    """Async function to process PDF and extract chapters"""
    temp_pdf_path = None
    
    try:
        # Initialize storage client
        try:
            storage_client = storage.Client()
            bucket = storage_client.bucket(processor.bucket_name)
            blob = bucket.blob(file_name)
            logger.info(f"Initialized GCS client for bucket: {processor.bucket_name}")
        except Exception as storage_error:
            logger.error(f"Failed to initialize GCS client: {storage_error}")
            raise Exception(f"Storage initialization failed: {str(storage_error)}")
        
        # Create temporary file for PDF processing
        try:
            temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            temp_pdf_path = temp_pdf.name
            temp_pdf.close()
            logger.info(f"Created temporary file: {temp_pdf_path}")
        except Exception as temp_error:
            logger.error(f"Failed to create temporary file: {temp_error}")
            raise Exception(f"Temporary file creation failed: {str(temp_error)}")
        
        # Download PDF to temporary file
        try:
            logger.info(f"Downloading PDF {file_name} to {temp_pdf_path}")
            blob.download_to_filename(temp_pdf_path)
            logger.info(f"Successfully downloaded PDF to temporary file: {temp_pdf_path}")
            
            # Verify file exists and has content
            if not os.path.exists(temp_pdf_path):
                raise Exception("Downloaded file does not exist")
            
            file_size = os.path.getsize(temp_pdf_path)
            if file_size == 0:
                raise Exception("Downloaded file is empty")
            
            logger.info(f"Downloaded PDF file size: {file_size} bytes")
            
        except Exception as download_error:
            logger.error(f"Failed to download PDF: {download_error}")
            raise Exception(f"PDF download failed: {str(download_error)}")
        
        # Process the book
        try:
            logger.info(f"Starting book processing for: {temp_pdf_path}")
            result = await processor.process_book_to_gcs_with_pubsub(temp_pdf_path)
            logger.info(f"Book processing completed successfully")
            return result
        except Exception as processing_error:
            logger.error(f"Book processing failed: {processing_error}")
            raise Exception(f"Book processing failed: {str(processing_error)}")
            
    except Exception as e:
        logger.error(f"Error in async processing: {str(e)}")
        raise
        
    finally:
        # Clean up temporary file
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            try:
                os.unlink(temp_pdf_path)
                logger.info(f"Cleaned up temporary file: {temp_pdf_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to clean up temporary file {temp_pdf_path}: {cleanup_error}")


class CloudBookProcessor(BookProcessor):
    """Extended BookProcessor for Cloud Functions with GCS, Firestore, and Pub/Sub integration"""
    
    def __init__(self, bucket_name: str, book_id: str):
        logger.info(f"Initializing CloudBookProcessor for bucket: {bucket_name}, book: {book_id}")
        self.bucket_name = bucket_name
        self.book_id = book_id
        
        # Get environment configuration first (needed for secret retrieval)
        self.environment = os.environ.get("ENVIRONMENT", "dev")
        self.chapter_topic_name = os.environ.get("CHAPTER_TOPIC_NAME", f"tts-{self.environment}-chapter-process")
        logger.info(f"Environment: {self.environment}, Topic: {self.chapter_topic_name}")
        
        # Try multiple sources for project ID
        self.project_id = (
            os.environ.get("GCP_PROJECT") or 
            os.environ.get("GOOGLE_CLOUD_PROJECT") or 
            os.environ.get("GCLOUD_PROJECT") or
            self._get_project_id_from_metadata()
        )
        
        logger.info(f"Resolved project ID: {self.project_id}")
        
        if not self.project_id:
            raise ValueError("Project ID not found in environment variables or metadata")
        
        # Get API key from environment variable (set by Secret Manager)
        # Debug: Log available environment variables for troubleshooting
        logger.error(f"DEBUG: All environment variables: {list(os.environ.keys())}")
        logger.error(f"DEBUG: GEMINI variables: {[k for k in os.environ.keys() if 'GEMINI' in k]}")
        
        api_key = os.environ.get("GEMINI_API_KEY")
        logger.error(f"DEBUG: GEMINI_API_KEY value: {api_key[:20] if api_key else 'None'}...")
        
        if not api_key:
            logger.error("GEMINI_API_KEY environment variable not found")
            logger.error(f"All env vars containing 'API': {[k for k in os.environ.keys() if 'API' in k]}")
            raise ValueError("GEMINI_API_KEY environment variable not found")
        logger.info("Successfully retrieved Gemini API key from environment variable")
        
        try:
            logger.error("DEBUG: About to call super().__init__")
            super().__init__(api_key=api_key)
            logger.error("DEBUG: super().__init__ completed successfully")
        except Exception as init_error:
            logger.error(f"DEBUG: super().__init__ failed with error: {init_error}")
            logger.error(f"DEBUG: Error type: {type(init_error)}")
            raise
        
        logger.info("CloudBookProcessor initialization completed successfully")
        
        # Initialize clients with gRPC configuration
        try:
            # Force remove GOOGLE_APPLICATION_CREDENTIALS to use default authentication
            if 'GOOGLE_APPLICATION_CREDENTIALS' in os.environ:
                logger.error("DEBUG: Removing GOOGLE_APPLICATION_CREDENTIALS from environment")
                del os.environ['GOOGLE_APPLICATION_CREDENTIALS']
            
            logger.error("DEBUG: About to initialize storage client")
            self.storage_client = storage.Client()
            logger.error("DEBUG: Storage client initialized successfully")
            
            logger.error("DEBUG: About to get bucket")
            self.bucket = self.storage_client.bucket(bucket_name)
            logger.error("DEBUG: Bucket obtained successfully")
            
            # Cross-project Firestore configuration with gRPC settings
            firestore_project = os.environ.get("FIRESTORE_PROJECT_ID", self.project_id)
            logger.info(f"Using Firestore project: {firestore_project}")
            
            logger.error("DEBUG: About to initialize Firestore client")
            # Use connection pooling and limit concurrent requests
            self.firestore_client = firestore.Client(
                project=firestore_project,
                database="asia-southeast-1"
            )
            logger.error("DEBUG: Firestore client initialized successfully")
            
            logger.error("DEBUG: About to initialize Pub/Sub publisher")
            # Configure Pub/Sub with batch and flow control settings
            batch_settings = pubsub_v1.types.BatchSettings(
                max_messages=100,
                max_bytes=1024 * 1024,  # 1MB
                max_latency=0.1,  # 100ms
            )
            
            self.publisher = pubsub_v1.PublisherClient(batch_settings)
            logger.error("DEBUG: Pub/Sub publisher initialized successfully")
            
            logger.error("DEBUG: About to set topic path")
            self.chapter_topic_path = self.publisher.topic_path(self.project_id, self.chapter_topic_name)
            logger.error("DEBUG: Topic path set successfully")
            
        except Exception as client_error:
            logger.error(f"DEBUG: Client initialization failed: {client_error}")
            logger.error(f"DEBUG: Client error type: {type(client_error)}")
            # Add a small delay and retry once for gRPC initialization issues
            import time
            time.sleep(1)
            try:
                logger.info("Retrying client initialization after gRPC delay...")
                self.storage_client = storage.Client()
                self.bucket = self.storage_client.bucket(bucket_name)
                firestore_project = os.environ.get("FIRESTORE_PROJECT_ID", self.project_id)
                self.firestore_client = firestore.Client(
                    project=firestore_project,
                    database="asia-southeast-1"
                )
                batch_settings = pubsub_v1.types.BatchSettings(
                    max_messages=100,
                    max_bytes=1024 * 1024,
                    max_latency=0.1,
                )
                self.publisher = pubsub_v1.PublisherClient(batch_settings)
                self.chapter_topic_path = self.publisher.topic_path(self.project_id, self.chapter_topic_name)
                logger.info("Client initialization retry successful")
            except Exception as retry_error:
                logger.error(f"Client initialization retry also failed: {retry_error}")
                raise client_error
    
    def _get_project_id_from_metadata(self) -> Optional[str]:
        """Get project ID from Google Cloud metadata server"""
        try:
            import urllib.request
            import urllib.error
            
            # Metadata server URL for project ID
            metadata_url = "http://metadata.google.internal/computeMetadata/v1/project/project-id"
            request = urllib.request.Request(metadata_url)
            request.add_header("Metadata-Flavor", "Google")
            
            with urllib.request.urlopen(request, timeout=5) as response:
                project_id = response.read().decode('utf-8')
                logger.info(f"Retrieved project ID from metadata: {project_id}")
                return project_id
                
        except (urllib.error.URLError, Exception) as e:
            logger.warning(f"Could not retrieve project ID from metadata: {e}")
            return None
        
    
    def update_book_status(self, status: str, **kwargs) -> None:
        """Update book status in Firestore"""
        try:
            book_ref = self.firestore_client.collection('books-tts').document(self.book_id)
            
            update_data = {
                'status': status,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Add additional fields if provided
            if 'error_message' in kwargs:
                update_data['lastError'] = {
                    'message': kwargs['error_message'],
                    'at': firestore.SERVER_TIMESTAMP
                }
            
            if 'chapter_count' in kwargs:
                update_data['chapterCount'] = kwargs['chapter_count']
            
            if 'completed_chapters' in kwargs:
                update_data['completedChapters'] = kwargs['completed_chapters']
            
            # Use merge=True to update only specified fields
            book_ref.set(update_data, merge=True)
            logger.info(f"Updated book {self.book_id} status to: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update book status in Firestore: {e}")
            # Don't raise here - status updates are non-critical
    
    def generate_chapter_checksum(self, content: str) -> str:
        """Generate SHA256 checksum for chapter content"""
        import hashlib
        return f"sha256:{hashlib.sha256(content.encode('utf-8')).hexdigest()}"
    
    def save_manifest_to_gcs(self, manifest_data: Dict[str, Any]) -> str:
        """Save book manifest JSON to GCS"""
        file_name = f"books/{self.book_id}/book_manifest.json"
        
        blob = self.bucket.blob(file_name)
        blob.upload_from_string(
            json.dumps(manifest_data, indent=2, ensure_ascii=False),
            content_type='application/json'
        )
        logger.info(f"Updated book manifest: {file_name}")
        return file_name
    
    def create_chapter_firestore_doc(self, chapter_data: Dict[str, Any]) -> str:
        """Create/update chapter document in Firestore and return chapter UUID"""
        try:
            # Create a new document with auto-generated UUID
            chapters_collection = (
                self.firestore_client
                .collection('books-tts')
                .document(self.book_id)
                .collection('chapters')
            )
            
            chapter_content = chapter_data['content']
            firestore_data = {
                'bookId': self.book_id,
                'title': chapter_data['chapter_title'],
                'orderIndex': chapter_data['chapter_number'],
                'content': chapter_content,
                'contentChecksum': self.generate_chapter_checksum(chapter_content),
                'status': 'queued',  # Ready for TTS processing
                'attempts': 0,
                'createdAt': firestore.SERVER_TIMESTAMP,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Add optional fields
            if 'start_page' in chapter_data:
                firestore_data['startPage'] = chapter_data['start_page']
            if 'end_page' in chapter_data:
                firestore_data['endPage'] = chapter_data['end_page']
            if 'word_count' in chapter_data:
                firestore_data['wordCount'] = len(chapter_content.split())  # Calculate word count
            
            # Create document and get the auto-generated ID
            _, chapter_ref = chapters_collection.add(firestore_data)
            chapter_id = chapter_ref.id
            logger.info(f"Created Firestore doc for chapter {chapter_data['chapter_number']} with ID: {chapter_id}")
            return chapter_id
            
        except Exception as e:
            logger.error(f"Failed to create Firestore doc for chapter: {e}")
            # Don't raise - this is non-critical
            return None
    
    def publish_chapter_message(self, chapter_data: Dict[str, Any], chapter_id: str) -> None:
        """Publish chapter processing message to Pub/Sub"""
        try:
            correlation_id = f"{self.book_id}-{chapter_id}-{int(datetime.utcnow().timestamp())}"
            message_data = {
                'bookId': self.book_id,
                'chapterId': chapter_id,  # Use UUID instead of numbered chapter ID
                'chapterOrder': chapter_data['chapter_number'],
                'title': chapter_data['chapter_title'],
                'language': 'vi-VN',  # Default to Vietnamese - can be configured
                'content': chapter_data['content'],  # Include content directly in message
                'outputBucket': self.bucket_name,
                'outputPath': f"/books/{self.book_id}/audio/{chapter_id}.mp3",  # Use UUID for audio filename
                'correlationId': correlation_id,
                'createdAt': datetime.utcnow().isoformat()
            }
            
            # Validate message data
            if not message_data['content'] or len(message_data['content'].strip()) == 0:
                logger.warning(f"Skipping empty chapter content for chapter {chapter_data['chapter_number']}")
                return
            
            # Publish message
            try:
                message_json = json.dumps(message_data, ensure_ascii=False)
                message_bytes = message_json.encode('utf-8')
                
                # Check message size (Pub/Sub limit is 10MB)
                if len(message_bytes) > 10 * 1024 * 1024:
                    logger.error(f"Chapter {chapter_data['chapter_number']} message too large: {len(message_bytes)} bytes")
                    return
                
                future = self.publisher.publish(self.chapter_topic_path, message_bytes)
                message_id = future.result(timeout=30)  # 30 second timeout
                
                logger.info(f"Published chapter {chapter_data['chapter_number']} message: {message_id}")
                
            except Exception as pub_error:
                logger.error(f"Pub/Sub publish failed for chapter {chapter_data['chapter_number']}: {pub_error}")
                # Don't raise - continue processing other chapters
            
        except Exception as e:
            logger.error(f"Failed to prepare chapter message for chapter {chapter_data.get('chapter_number', 'unknown')}: {e}")
            # Don't raise - continue processing other chapters
    
    def create_chapter_json(self, chapter, book_outline=None) -> Dict[str, Any]:
        """Create chapter JSON schema compatible with the migration plan"""
        # Clean up content by removing extra whitespace and normalizing newlines
        clean_content = chapter.content
        if clean_content:
            # Replace multiple consecutive newlines with single newline
            clean_content = re.sub(r'\n+', '\n', clean_content)
            # Remove trailing/leading whitespace
            clean_content = clean_content.strip()
            # Replace newlines with spaces for TTS processing
            clean_content = clean_content.replace('\n', ' ')
            # Clean up multiple consecutive spaces
            clean_content = re.sub(r'\s+', ' ', clean_content)
            
            # Sanitize content to prevent JSON encoding issues
            # Remove or escape potentially problematic characters
            clean_content = clean_content.replace('\\', '\\\\')  # Escape backslashes
            clean_content = clean_content.replace('"', '\\"')   # Escape quotes
            clean_content = clean_content.replace('\t', ' ')    # Replace tabs with spaces
            clean_content = clean_content.replace('\r', ' ')    # Replace carriage returns
            # Remove other control characters that might cause JSON issues
            clean_content = re.sub(r'[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]', '', clean_content)
        
        return {
            "book_id": self.book_id,
            "chapter_number": chapter.chapter_number,
            "chapter_title": chapter.chapter_title,
            "start_page": chapter.start_page,
            "end_page": chapter.end_page,
            "content": clean_content,
            "word_count": len(clean_content.split()) if clean_content else 0,
            "status": chapter.status,
            "created_at": datetime.utcnow().isoformat()
        }
    
    def create_book_firestore_doc(self, outline, **kwargs) -> None:
        """Create/update main book document in Firestore"""
        try:
            book_ref = self.firestore_client.collection('books-tts').document(self.book_id)
            
            # Extract book title from first chapter or use book_id
            title = kwargs.get('title', self.book_id)
            if outline and len(outline) > 0:
                # Try to extract book title from outline structure
                first_chapter = outline[0]
                if 'introduction' in first_chapter.get('chapter_title', '').lower():
                    title = kwargs.get('title', f"Book {self.book_id}")
            
            firestore_data = {
                'title': title,
                'language': kwargs.get('language', 'vi-VN'),
                'sourcePdfPath': f"/books/{self.book_id}/book.pdf",  # Relative path
                'status': kwargs.get('status', 'new'),
                'chapterCount': len(outline),
                'completedChapters': kwargs.get('completed_chapters', 0),
                'createdAt': firestore.SERVER_TIMESTAMP,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Add optional fields
            if 'author' in kwargs:
                firestore_data['author'] = kwargs['author']
            if 'metadata' in kwargs:
                firestore_data['metadata'] = kwargs['metadata']
            
            book_ref.set(firestore_data, merge=True)
            logger.info(f"Updated Firestore doc for book {self.book_id}")
            
        except Exception as e:
            logger.error(f"Failed to update book Firestore doc: {e}")
    
    def create_manifest(self, outline, processing_status="processing", completed_chapters=0, failed_chapters=None) -> Dict[str, Any]:
        """Create book manifest JSON"""
        if failed_chapters is None:
            failed_chapters = []
            
        return {
            "book_id": self.book_id,
            "total_chapters": len(outline),
            "completed_chapters": completed_chapters,
            "failed_chapters": failed_chapters,
            "processing_status": processing_status,
            "created_at": datetime.utcnow().isoformat(),
            "outline": outline
        }
    
    async def process_book_to_gcs_with_pubsub(self, pdf_path: str) -> Dict[str, Any]:
        """Main processing workflow for GCS with Pub/Sub integration"""
        failed_chapters = []
        completed_count = 0
        book_outline = []
        
        def progress_callback(message):
            logger.info(f"Processing progress: {message}")
        
        def chapter_update_callback(chapter):
            nonlocal completed_count, failed_chapters, book_outline
            
            logger.info(f"Chapter {chapter.chapter_number} ({chapter.chapter_title}): {chapter.status}")
            
            if chapter.status == 'completed':
                # Create chapter data structure
                chapter_json = self.create_chapter_json(chapter, book_outline)
                
                # Create Firestore document for the chapter (returns UUID)
                chapter_id = self.create_chapter_firestore_doc(chapter_json)
                
                if chapter_id:
                    # Publish message to Pub/Sub for TTS processing
                    self.publish_chapter_message(chapter_json, chapter_id)
                
                completed_count += 1
                
                # Update book status to show text extraction progress (not TTS completion)
                self.update_book_status(
                    status="extracting", 
                    completed_chapters=0,  # Don't update this until TTS is complete
                    chapter_count=len(book_outline)
                )
                
                # Update manifest with progress
                manifest = self.create_manifest(
                    outline=book_outline,
                    processing_status="processing",
                    completed_chapters=completed_count,
                    failed_chapters=failed_chapters
                )
                self.save_manifest_to_gcs(manifest)
                
            elif chapter.status == 'error':
                failed_chapters.append(chapter.chapter_number)
                logger.error(f"Chapter {chapter.chapter_number} failed: {chapter.errorMessage}")
        
        try:
            # Step 1: Find ToC
            logger.info("Finding Table of Contents...")
            toc_pages = await self.find_table_of_contents_pages(pdf_path, progress_callback)
            
            # Step 2: Get outline
            logger.info("Extracting book outline...")
            outline = await self.get_book_outline(pdf_path, progress_callback, toc_pages)
            book_outline = outline  # Store outline for use in callbacks
            logger.info(f"Found {len(outline)} chapters")
            
            # Create book document in Firestore
            self.create_book_firestore_doc(outline, status="extracting")
            
            # Create initial manifest with outline
            manifest = self.create_manifest(
                outline=outline,
                processing_status="processing", 
                completed_chapters=0,
                failed_chapters=[]
            )
            self.save_manifest_to_gcs(manifest)
            
            # Step 3: Process chapters in parallel
            logger.info("Processing chapters in parallel...")
            await self.process_chapters_in_parallel(pdf_path, outline, chapter_update_callback)
            
            # Step 4: Update status to indicate text extraction is complete, TTS is starting
            final_status = "text_extracted" if len(failed_chapters) == 0 else "partial_text_extraction"
            
            # Update book status to show text extraction is done, TTS processing starting
            self.update_book_status(
                status=final_status,
                completed_chapters=0,  # Reset to 0 - TTS processing will increment this
                chapter_count=len(outline)
            )
            
            # Final manifest update - showing text extraction complete, TTS queued
            final_manifest = self.create_manifest(
                outline=outline,
                processing_status="tts_queued",  # Indicates messages sent to TTS queue
                completed_chapters=0,  # TTS completion will be tracked separately
                failed_chapters=failed_chapters
            )
            self.save_manifest_to_gcs(final_manifest)
            
            logger.info(f"Book processing complete. Completed: {completed_count}, Failed: {len(failed_chapters)}")
            
            return {
                "status": "success",
                "book_id": self.book_id,
                "total_chapters": len(outline),
                "completed_chapters": completed_count,
                "failed_chapters": failed_chapters,
                "processing_status": final_status
            }
            
        except Exception as e:
            logger.error(f"Error during book processing: {e}")
            
            # Update error status in Firestore
            self.update_book_status(
                status="error",
                error_message=str(e),
                completed_chapters=completed_count
            )
            
            # Save error manifest
            error_manifest = self.create_manifest(
                outline=book_outline,
                processing_status="error",
                completed_chapters=completed_count,
                failed_chapters=failed_chapters
            )
            self.save_manifest_to_gcs(error_manifest)
            
            raise